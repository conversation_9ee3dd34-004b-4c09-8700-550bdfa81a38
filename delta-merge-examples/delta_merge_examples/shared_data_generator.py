"""
Shared data generation functions for both Delta Lake and DuckLake testing.
This module provides format-agnostic data generation that can be used by both systems.
"""

import random
import pandas as pd
import pyarrow as pa
from datetime import datetime, timedelta
from typing import Union, List, Dict, Any, Optional
from pathlib import Path


def generate_transaction_data(file_id: str, num_rows: int, base_date: Optional[datetime] = None) -> pd.DataFrame:
    """
    Generate realistic transaction data as a pandas DataFrame.
    
    Args:
        file_id: Identifier for the file/batch
        num_rows: Number of rows to generate
        base_date: Base date for transaction dates (defaults to 2024-01-01)
    
    Returns:
        pandas DataFrame with transaction data
    """
    if base_date is None:
        base_date = datetime(2024, 1, 1)
    
    if num_rows == 0:
        # Return empty DataFrame with correct schema
        return pd.DataFrame({
            'file_id': pd.Series([], dtype='string'),
            'customer_id': pd.Series([], dtype='string'),
            'transaction_id': pd.Series([], dtype='string'),
            'amount': pd.Series([], dtype='float64'),
            'transaction_date': pd.Series([], dtype='datetime64[ns]'),
            'processed_at': pd.Series([], dtype='datetime64[ns]')
        })
    
    # Generate data
    data = []
    for i in range(num_rows):
        customer_id = f"cust_{i+1:06d}" if num_rows > 1000 else f"cust_{i+1:03d}"
        transaction_id = f"txn_{i+1:06d}" if num_rows > 1000 else f"txn_{i+1:03d}"
        
        # Random amount between $10 and $1000
        amount = round(random.uniform(10.0, 1000.0), 2)
        
        # Random transaction date within 30 days of base_date
        days_offset = random.randint(0, 30)
        transaction_date = base_date + timedelta(days=days_offset)
        
        # Processed timestamp (recent)
        processed_at = datetime.now()
        
        data.append({
            'file_id': file_id,
            'customer_id': customer_id,
            'transaction_id': transaction_id,
            'amount': amount,
            'transaction_date': transaction_date,
            'processed_at': processed_at
        })
    
    return pd.DataFrame(data)


def generate_realistic_file_sizes(total_files: int, total_rows: int, large_file_ratio: float = 1/50) -> List[int]:
    """
    Generate realistic file size distribution with skew.
    
    Args:
        total_files: Total number of files
        total_rows: Total number of rows to distribute
        large_file_ratio: Ratio of files that should be large (default: 1 in 50)
    
    Returns:
        List of row counts for each file
    """
    file_sizes = []
    
    # Determine which files will be large
    num_large_files = max(1, int(total_files * large_file_ratio))
    large_file_indices = random.sample(range(total_files), num_large_files)
    
    # Calculate row distribution
    large_file_rows = int(total_rows * 0.7)  # 70% of data in large files
    small_file_rows = total_rows - large_file_rows
    
    rows_per_large_file = large_file_rows // num_large_files if num_large_files > 0 else 0
    small_files_count = total_files - num_large_files
    rows_per_small_file = small_file_rows // small_files_count if small_files_count > 0 else 0
    
    for i in range(total_files):
        if i in large_file_indices:
            # Large files: 5000-15000 rows
            size = max(5000, rows_per_large_file + random.randint(-1000, 1000))
        else:
            # Small files: 10-500 rows
            size = max(10, rows_per_small_file + random.randint(-50, 50))
        file_sizes.append(size)
    
    return file_sizes


def generate_batch_data(file_ids: List[str], file_sizes: List[int]) -> pd.DataFrame:
    """
    Generate data for multiple files in a batch.
    
    Args:
        file_ids: List of file identifiers
        file_sizes: List of row counts for each file
    
    Returns:
        Combined pandas DataFrame for all files
    """
    if len(file_ids) != len(file_sizes):
        raise ValueError("file_ids and file_sizes must have the same length")
    
    all_data = []
    for file_id, size in zip(file_ids, file_sizes):
        if size > 0:
            file_data = generate_transaction_data(file_id, size)
            all_data.append(file_data)
    
    if all_data:
        return pd.concat(all_data, ignore_index=True)
    else:
        # Return empty DataFrame with correct schema
        return generate_transaction_data("empty", 0)


def dataframe_to_pyarrow(df: pd.DataFrame) -> pa.Table:
    """Convert pandas DataFrame to PyArrow Table."""
    return pa.Table.from_pandas(df)


def create_file_metadata(file_ids: List[str], file_sizes: List[int]) -> Dict[str, Dict[str, Any]]:
    """
    Create metadata dictionary for tracking file information.
    
    Args:
        file_ids: List of file identifiers
        file_sizes: List of row counts for each file
    
    Returns:
        Dictionary mapping file_id to metadata
    """
    metadata = {}
    for i, (file_id, size) in enumerate(zip(file_ids, file_sizes)):
        metadata[file_id] = {
            'size': size,
            'is_large': size > 1000,
            'index': i
        }
    return metadata


# Convenience functions for backward compatibility with existing code
def create_initial_table_data() -> pa.Table:
    """Create initial table data (6 rows across 3 files) - backward compatibility."""
    df = generate_batch_data(
        file_ids=['file_A', 'file_B', 'file_C'],
        file_sizes=[3, 2, 1]
    )
    return dataframe_to_pyarrow(df)


def create_large_file_data(file_id: str, num_rows: int) -> pa.Table:
    """Create data for a single large file - backward compatibility."""
    df = generate_transaction_data(file_id, num_rows)
    return dataframe_to_pyarrow(df)


def create_file_replacement_same_rows() -> pa.Table:
    """Create replacement data for file_A with same number of rows - backward compatibility."""
    df = generate_transaction_data('file_A', 3)
    return dataframe_to_pyarrow(df)


def generate_test_parquet_files(file_ids: List[str], file_sizes: List[int], output_dir: str) -> List[str]:
    """
    Generate test Parquet files directly without pandas in the critical path.

    Args:
        file_ids: List of file identifiers
        file_sizes: List of row counts for each file
        output_dir: Directory to save Parquet files

    Returns:
        List of generated Parquet file paths
    """
    from pathlib import Path
    import pyarrow.parquet as pq

    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)

    parquet_files = []

    for file_id, size in zip(file_ids, file_sizes):
        # Generate data using existing function
        df = generate_transaction_data(file_id, size)
        table = dataframe_to_pyarrow(df)

        # Save as Parquet
        parquet_file = output_path / f"{file_id}.parquet"
        pq.write_table(table, str(parquet_file))
        parquet_files.append(str(parquet_file))

    return parquet_files


def create_batch_test_data(batch_id: str, file_count: int, total_rows: int, output_dir: str) -> Dict[str, Any]:
    """
    Create a batch of test Parquet files for realistic ETL testing.

    Args:
        batch_id: Identifier for this batch
        file_count: Number of files in the batch
        total_rows: Total rows to distribute across files
        output_dir: Directory to save files

    Returns:
        Dictionary with batch metadata and file paths
    """
    # Generate realistic file sizes for the batch
    file_sizes = generate_realistic_file_sizes(file_count, total_rows, large_file_ratio=1/10)
    file_ids = [f"{batch_id}_file_{i:03d}" for i in range(file_count)]

    # Generate Parquet files
    parquet_files = generate_test_parquet_files(file_ids, file_sizes, output_dir)

    return {
        'batch_id': batch_id,
        'file_count': file_count,
        'total_rows': total_rows,
        'file_metadata': create_file_metadata(file_ids, file_sizes),
        'parquet_files': parquet_files
    }


class ParquetDatasetManager:
    """Manages pre-generated Parquet files for consistent testing across formats."""

    def __init__(self, base_path: str = "./test_datasets"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(exist_ok=True)
        self.metadata_file = self.base_path / "dataset_metadata.json"

    def generate_dataset(self, dataset_name: str, total_files: int = 2000,
                        total_rows: int = 1000000, large_file_ratio: float = 1/50) -> Dict[str, Any]:
        """
        Generate a complete dataset as Parquet files.

        Args:
            dataset_name: Name for this dataset
            total_files: Total number of files to generate
            total_rows: Total rows to distribute across files
            large_file_ratio: Ratio of files that should be large

        Returns:
            Dictionary with dataset metadata
        """
        dataset_path = self.base_path / dataset_name
        dataset_path.mkdir(exist_ok=True)

        print(f"Generating dataset '{dataset_name}':")
        print(f"   {total_files:,} files, {total_rows:,} total rows")
        print(f"   Saving to: {dataset_path}")

        # Generate realistic file size distribution
        file_sizes = generate_realistic_file_sizes(total_files, total_rows, large_file_ratio)
        file_ids = [f"file_{i:04d}" for i in range(total_files)]

        # Create metadata
        metadata = {
            'dataset_name': dataset_name,
            'total_files': total_files,
            'total_rows': total_rows,
            'large_file_ratio': large_file_ratio,
            'file_metadata': create_file_metadata(file_ids, file_sizes),
            'parquet_files': []
        }

        # Generate Parquet files
        batch_size = 100  # Process in batches to manage memory
        for batch_start in range(0, total_files, batch_size):
            batch_end = min(batch_start + batch_size, total_files)
            print(f"   Generating files {batch_start:,}-{batch_end-1:,}")

            for i in range(batch_start, batch_end):
                file_id = file_ids[i]
                file_size = file_sizes[i]

                # Generate data for this file
                df = generate_transaction_data(file_id, file_size)
                table = dataframe_to_pyarrow(df)

                # Save as Parquet
                parquet_file = dataset_path / f"{file_id}.parquet"
                import pyarrow.parquet as pq
                pq.write_table(table, str(parquet_file))

                metadata['parquet_files'].append({
                    'file_id': file_id,
                    'parquet_path': str(parquet_file),
                    'row_count': file_size,
                    'is_large': file_size > 1000
                })

        # Save metadata
        import json
        with open(self.metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)

        print(f"Dataset '{dataset_name}' generated successfully")
        print(f"   {len(metadata['parquet_files'])} Parquet files created")

        large_files = [f for f in metadata['parquet_files'] if f['is_large']]
        small_files = [f for f in metadata['parquet_files'] if not f['is_large']]
        print(f"   Distribution: {len(large_files)} large files, {len(small_files)} small files")

        return metadata

    def load_dataset_metadata(self, dataset_name: str) -> Dict[str, Any]:
        """Load metadata for an existing dataset."""
        if not self.metadata_file.exists():
            raise FileNotFoundError(f"No dataset metadata found at {self.metadata_file}")

        import json
        with open(self.metadata_file, 'r') as f:
            metadata = json.load(f)

        if metadata['dataset_name'] != dataset_name:
            raise ValueError(f"Metadata is for dataset '{metadata['dataset_name']}', not '{dataset_name}'")

        return metadata

    def get_parquet_files_for_batch(self, metadata: Dict[str, Any],
                                   start_idx: int, end_idx: int) -> List[str]:
        """Get list of Parquet file paths for a batch of files."""
        parquet_files = metadata['parquet_files'][start_idx:end_idx]
        return [f['parquet_path'] for f in parquet_files]

    def get_file_by_id(self, metadata: Dict[str, Any], file_id: str) -> Dict[str, Any]:
        """Get Parquet file info by file_id."""
        for file_info in metadata['parquet_files']:
            if file_info['file_id'] == file_id:
                return file_info
        raise ValueError(f"File {file_id} not found in dataset")

    def get_large_files(self, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get all large files from the dataset."""
        return [f for f in metadata['parquet_files'] if f['is_large']]

    def get_small_files(self, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get all small files from the dataset."""
        return [f for f in metadata['parquet_files'] if not f['is_large']]

    def cleanup_dataset(self, dataset_name: str):
        """Remove a dataset and its files."""
        dataset_path = self.base_path / dataset_name
        if dataset_path.exists():
            import shutil
            shutil.rmtree(dataset_path)

        if self.metadata_file.exists():
            self.metadata_file.unlink()

    def dataset_exists(self, dataset_name: str) -> bool:
        """Check if a dataset already exists."""
        try:
            metadata = self.load_dataset_metadata(dataset_name)
            dataset_path = self.base_path / dataset_name
            return dataset_path.exists() and len(list(dataset_path.glob("*.parquet"))) > 0
        except:
            return False
