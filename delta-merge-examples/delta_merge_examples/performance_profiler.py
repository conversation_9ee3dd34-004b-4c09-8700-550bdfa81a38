"""
Performance profiling tools for Delta Lake and DuckLake operations.
Provides detailed timing, memory profiling, and query analysis.
"""

import time
import psutil
import cProfile
import pstats
import io
from contextlib import contextmanager
from typing import Dict, Any, List
import pandas as pd
from deltalake import DeltaTable
import duckdb


class PerformanceProfiler:
    """Comprehensive performance profiling for lakehouse operations."""
    
    def __init__(self):
        self.results = []
        self.current_profile = None
    
    @contextmanager
    def profile_operation(self, operation_name: str, enable_cprofile: bool = True):
        """Context manager for profiling operations with detailed metrics."""
        print(f"\n🔍 PROFILING: {operation_name}")
        print("-" * 60)
        
        # Initial system state
        process = psutil.Process()
        start_memory = process.memory_info().rss / 1024 / 1024
        start_cpu = process.cpu_percent()
        start_time = time.time()
        
        # Start cProfile if enabled
        profiler = None
        if enable_cprofile:
            profiler = cProfile.Profile()
            profiler.enable()
        
        try:
            yield self
            
        finally:
            # Stop profiling
            if profiler:
                profiler.disable()
            
            # Final system state
            end_time = time.time()
            end_memory = process.memory_info().rss / 1024 / 1024
            end_cpu = process.cpu_percent()
            
            # Calculate metrics
            execution_time = end_time - start_time
            memory_delta = end_memory - start_memory
            
            result = {
                'operation': operation_name,
                'execution_time': execution_time,
                'start_memory_mb': start_memory,
                'end_memory_mb': end_memory,
                'memory_delta_mb': memory_delta,
                'start_cpu_percent': start_cpu,
                'end_cpu_percent': end_cpu
            }
            
            # Add profiling data if available
            if profiler:
                s = io.StringIO()
                ps = pstats.Stats(profiler, stream=s)
                ps.sort_stats('cumulative')
                ps.print_stats(20)  # Top 20 functions
                result['profile_data'] = s.getvalue()
            
            self.results.append(result)
            self.current_profile = result
            
            # Print immediate results
            print(f"Execution time: {execution_time:.3f}s")
            print(f"Memory change: {memory_delta:+.1f} MB")
            print(f"CPU usage: {start_cpu:.1f}% → {end_cpu:.1f}%")
    
    def print_profile_summary(self, operation_name: str = None):
        """Print detailed profiling summary."""
        if operation_name:
            results = [r for r in self.results if r['operation'] == operation_name]
        else:
            results = self.results
        
        if not results:
            print("No profiling results available")
            return
        
        result = results[-1]  # Most recent
        
        print(f"\nDETAILED PROFILE: {result['operation']}")
        print("=" * 70)
        print(f"Total execution time: {result['execution_time']:.3f}s")
        print(f"Memory usage: {result['start_memory_mb']:.1f} → {result['end_memory_mb']:.1f} MB ({result['memory_delta_mb']:+.1f})")
        print(f"CPU usage: {result['start_cpu_percent']:.1f}% → {result['end_cpu_percent']:.1f}%")

        if 'profile_data' in result:
            print(f"\nTOP FUNCTION CALLS:")
            print(result['profile_data'])


class DeltaLakeProfiler(PerformanceProfiler):
    """Specialized profiler for Delta Lake operations."""
    
    def __init__(self, table_path: str):
        super().__init__()
        self.table_path = table_path
    
    def profile_merge_operation(self, dt: DeltaTable, new_data, target_file: str):
        """Profile a MERGE operation with detailed breakdown."""
        with self.profile_operation(f"Delta MERGE - {target_file}"):
            print("🔄 Starting MERGE operation...")
            
            # Step 1: Merge setup and planning
            step_start = time.time()
            merge_builder = dt.merge(
                source=new_data,
                predicate="false",
                source_alias="source",
                target_alias="target"
            ).when_not_matched_by_source_delete(
                predicate=f"target.file_id = '{target_file}'"
            ).when_not_matched_insert({
                "file_id": "source.file_id",
                "customer_id": "source.customer_id",
                "transaction_id": "source.transaction_id",
                "amount": "source.amount",
                "transaction_date": "source.transaction_date",
                "processed_at": "source.processed_at"
            })
            setup_time = time.time() - step_start
            print(f"  📋 Merge setup: {setup_time:.3f}s")
            
            # Step 2: Execute merge
            step_start = time.time()
            result = merge_builder.execute()
            execution_time = time.time() - step_start
            print(f"  ⚡ Merge execution: {execution_time:.3f}s")
            
            return result
    
    def profile_native_operations(self, dt: DeltaTable, new_data, target_file: str):
        """Profile native DELETE + APPEND operations."""
        from deltalake import write_deltalake
        
        with self.profile_operation(f"Delta Native - {target_file}"):
            print("🗑️ Starting DELETE operation...")
            
            # Step 1: DELETE
            step_start = time.time()
            delete_result = dt.delete(predicate=f"file_id = '{target_file}'")
            delete_time = time.time() - step_start
            print(f"  🗑️  DELETE execution: {delete_time:.3f}s")
            
            # Step 2: APPEND
            print("➕ Starting APPEND operation...")
            step_start = time.time()
            write_deltalake(self.table_path, new_data, mode="append")
            append_time = time.time() - step_start
            print(f"  ➕ APPEND execution: {append_time:.3f}s")
            
            return {'delete': delete_result, 'delete_time': delete_time, 'append_time': append_time}
    
    def analyze_table_structure(self, dt: DeltaTable):
        """Analyze Delta table structure for performance insights."""
        print(f"\nDELTA TABLE ANALYSIS")
        print("=" * 50)

        # Basic stats
        table = dt.to_pyarrow_table()
        print(f"Total rows: {table.num_rows:,}")
        print(f"Total columns: {table.num_columns}")

        # File-level stats
        files = dt.files()
        print(f"Total files: {len(files)}")

        # Schema info
        schema = dt.schema()
        print(f"Schema: {schema}")

        # Version info
        print(f"Current version: {dt.version()}")

        # Partitioning info (if any)
        metadata = dt.metadata()
        if metadata.partition_columns:
            print(f"Partitioned by: {metadata.partition_columns}")
        else:
            print("No partitioning")


class DuckLakeProfiler(PerformanceProfiler):
    """Specialized profiler for DuckLake operations."""
    
    def __init__(self, conn: duckdb.DuckDBPyConnection):
        super().__init__()
        self.conn = conn
    
    def profile_sql_operation(self, operation_name: str, sql_query: str, explain: bool = True):
        """Profile a SQL operation with EXPLAIN ANALYZE."""
        with self.profile_operation(f"DuckLake {operation_name}"):
            if explain:
                print("📋 Query plan analysis...")
                explain_query = f"EXPLAIN ANALYZE {sql_query}"
                try:
                    result = self.conn.execute(explain_query).fetchall()
                    print("🔍 EXPLAIN ANALYZE results:")
                    for row in result:
                        print(f"  {row[0]}")
                except Exception as e:
                    print(f"⚠️  Could not get query plan: {e}")
            
            # Execute the actual query
            print("⚡ Executing query...")
            step_start = time.time()
            result = self.conn.execute(sql_query)
            execution_time = time.time() - step_start
            print(f"  ⚡ SQL execution: {execution_time:.3f}s")
            
            return result
    
    def profile_transaction_operation(self, operation_name: str, sql_queries: List[str]):
        """Profile a multi-statement transaction."""
        with self.profile_operation(f"DuckLake Transaction - {operation_name}"):
            print("🔄 Starting transaction...")
            
            # Begin transaction
            step_start = time.time()
            self.conn.execute("BEGIN TRANSACTION;")
            begin_time = time.time() - step_start
            print(f"  📋 BEGIN: {begin_time:.3f}s")
            
            try:
                # Execute each query
                for i, query in enumerate(sql_queries):
                    step_start = time.time()
                    result = self.conn.execute(query)
                    query_time = time.time() - step_start
                    print(f"  ⚡ Query {i+1}: {query_time:.3f}s")
                
                # Commit transaction
                step_start = time.time()
                self.conn.execute("COMMIT;")
                commit_time = time.time() - step_start
                print(f"  ✅ COMMIT: {commit_time:.3f}s")
                
                return True
                
            except Exception as e:
                # Rollback on error
                self.conn.execute("ROLLBACK;")
                print(f"❌ Transaction failed, rolled back: {e}")
                return False
    
    def analyze_table_structure(self, table_name: str = "demo"):
        """Analyze DuckLake table structure."""
        print(f"\nDUCKLAKE TABLE ANALYSIS")
        print("=" * 50)

        # Basic stats
        result = self.conn.execute(f"SELECT COUNT(*) FROM {table_name};").fetchone()
        total_rows = result[0] if result else 0
        print(f"Total rows: {total_rows:,}")

        # Schema info
        result = self.conn.execute(f"DESCRIBE {table_name};").fetchall()
        print(f"Schema:")
        for row in result:
            print(f"  {row[0]}: {row[1]}")

        # File distribution analysis
        try:
            result = self.conn.execute(f"""
                SELECT file_id, COUNT(*) as row_count
                FROM {table_name}
                GROUP BY file_id
                ORDER BY row_count DESC
                LIMIT 10;
            """).fetchall()
            print(f"Top 10 files by row count:")
            for row in result:
                print(f"  {row[0]}: {row[1]:,} rows")
        except Exception as e:
            print(f"Could not analyze file distribution: {e}")


def compare_profiling_results(delta_profiler: DeltaLakeProfiler, ducklake_profiler: DuckLakeProfiler):
    """Compare profiling results between Delta Lake and DuckLake."""
    print(f"\nPROFILING COMPARISON")
    print("=" * 70)

    delta_results = delta_profiler.results
    ducklake_results = ducklake_profiler.results

    if not delta_results or not ducklake_results:
        print("Insufficient profiling data for comparison")
        return
    
    print(f"{'Operation':<25} {'Delta (s)':<12} {'DuckLake (s)':<12} {'Winner':<12}")
    print("-" * 70)
    
    # Compare similar operations
    for delta_result in delta_results:
        delta_op = delta_result['operation']
        delta_time = delta_result['execution_time']
        
        # Find matching DuckLake operation
        matching_ducklake = None
        for ducklake_result in ducklake_results:
            if 'DELETE' in delta_op and 'DELETE' in ducklake_result['operation']:
                matching_ducklake = ducklake_result
                break
            elif 'APPEND' in delta_op and 'APPEND' in ducklake_result['operation']:
                matching_ducklake = ducklake_result
                break
        
        if matching_ducklake:
            ducklake_time = matching_ducklake['execution_time']
            winner = "Delta" if delta_time < ducklake_time else "DuckLake"
            print(f"{delta_op:<25} {delta_time:<12.3f} {ducklake_time:<12.3f} {winner:<12}")
        else:
            print(f"{delta_op:<25} {delta_time:<12.3f} {'N/A':<12} {'N/A':<12}")
