"""
Main script for Delta Lake vs DuckLake performance comparison.

This script provides:
1. Test data generation via Parquet files
2. 10M row dataset performance comparison
3. DuckLake vs Delta delete+append and merge comparison
4. Factual performance and feature information
"""

import sys
import argparse

from delta_merge_examples.scale_comparison_10m import run_10m_scale_comparison
from delta_merge_examples.shared_data_generator import ParquetDatasetManager


def generate_test_data():
    """Generate test datasets for performance comparison."""
    print("Generating test datasets...")
    manager = ParquetDatasetManager()

    # Generate 10M row dataset if it doesn't exist
    if not manager.dataset_exists("scale_10m_test"):
        print("Generating 10M row dataset...")
        manager.generate_dataset(
            dataset_name="scale_10m_test",
            total_files=5000,
            total_rows=10000000,
            large_file_ratio=1/50
        )
    else:
        print("10M row dataset already exists")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Delta Lake vs DuckLake performance comparison",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --generate-data          # Generate test datasets
  python main.py --compare-formats        # Run 10M row comparison (Delta vs DuckLake)
  python main.py --scale-10m              # Run 10M row scale comparison
        """
    )

    parser.add_argument("--generate-data", action="store_true",
                       help="Generate test datasets")
    parser.add_argument("--compare-formats", action="store_true",
                       help="Run 10M row comparison (Delta vs DuckLake)")
    parser.add_argument("--scale-10m", action="store_true",
                       help="Run 10M row scale comparison")

    args = parser.parse_args()

    # If no specific option is chosen, show help
    if not any([args.generate_data, args.compare_formats, args.scale_10m]):
        parser.print_help()
        return

    print("Delta Lake vs DuckLake Performance Comparison")
    print("="*80)
    print("Performance comparison between Delta Lake and DuckLake storage formats")
    print("="*80)

    try:
        if args.generate_data:
            print("\nGenerating test datasets...")
            generate_test_data()

        if args.compare_formats:
            print("\nRunning 10M row scale comparison (includes format comparison)...")
            run_10m_scale_comparison()

        if args.scale_10m:
            print("\nRunning 10M row scale comparison...")
            run_10m_scale_comparison()

        print("\n" + "="*80)
        print("Comparison completed successfully!")
        print("="*80)

    except KeyboardInterrupt:
        print("\n\nInterrupted by user")
    except Exception as e:
        print(f"\n\nError: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
