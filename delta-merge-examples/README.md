# Delta Lake vs DuckLake Performance Comparison

This package provides performance comparison between Delta Lake and DuckLake storage formats using realistic test scenarios. It focuses on factual performance data and feature differences.

## Overview

The comparison includes:

1. **Test data generation** via Parquet files
2. **10M row dataset** performance comparison
3. **DuckLake vs Delta** delete+append and merge operations
4. **Factual performance** and feature information

## Key Features

- 📊 **Test data generation** with realistic file size distributions
- ⚡ **10M row scale testing** for production-scale comparison
- 🔍 **Performance profiling** with detailed metrics
- 📈 **Memory and CPU usage** monitoring

## Installation

This project uses `uv` for dependency management:

```bash
# Install dependencies
uv sync

# Activate virtual environment
source .venv/bin/activate  # On Unix/macOS
# or
.venv\Scripts\activate     # On Windows
```

## Usage

Run the main script with different options:

```bash
# Generate test datasets
uv run python main.py --generate-data

# Run 10M row comparison (Delta vs DuckLake)
uv run python main.py --compare-formats

# Run 10M row scale comparison
uv run python main.py --scale-10m
```

## Features Included

### 1. Test Data Generation (`--generate-data`)

Generates realistic test datasets:
- 10M row dataset with 5,000 files
- Realistic file size distribution (1 in 50 files large)
- Saved as Parquet files for consistent testing

### 2. 10M Row Comparison (`--compare-formats`, `--scale-10m`)

Performance comparison at production scale:
- Delta Lake native operations (DELETE + APPEND)
- DuckLake operations (DELETE + INSERT + COMMIT)
- Small vs large file performance characteristics
- Memory usage and execution time metrics

## Key Differences

### Delta Lake Native Operations

```python
# Step 1: Delete
delete_result = dt.delete(predicate="file_id = 'file_A'")

# Step 2: Append
write_deltalake(table_path, new_data, mode="append")
```

**Characteristics:**
- 2 separate operations
- 2 table version increments
- Native Delta Lake API

### DuckLake Operations

```python
# Single transaction
conn.execute("BEGIN TRANSACTION;")
conn.execute("DELETE FROM demo WHERE file_id = 'file_A';")
conn.execute("INSERT INTO demo SELECT * FROM new_data;")
conn.execute("COMMIT;")
```

**Characteristics:**
- 1 SQL transaction
- 1 snapshot update
- SQL-based interface

## Performance Characteristics

The comparison provides factual data on:

- **Execution time** for different file sizes
- **Memory usage** patterns
- **Transaction model** differences
- **Scaling behavior** at 10M+ rows

## Project Structure

```
delta_merge_examples/
├── delta_merge_examples/
│   ├── __init__.py
│   ├── shared_data_generator.py    # Test data generation
│   ├── scale_comparison_10m.py     # 10M row comparison
│   └── performance_profiler.py     # Performance profiling tools
├── main.py                         # Main entry point
├── README.md
└── pyproject.toml
```

## Dependencies

- `deltalake`: Delta Lake Python bindings
- `duckdb`: DuckDB with DuckLake extension
- `pyarrow`: Arrow data processing
- `pandas`: Data manipulation
- `psutil`: System resource monitoring

## Contributing

This is a performance comparison tool. Feel free to extend it with additional test scenarios.